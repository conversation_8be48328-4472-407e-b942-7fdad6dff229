{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare && node scripts/prisma-generate.js", "prisma:generate": "node scripts/prisma-generate.js", "prisma:fix": "node scripts/fix-prisma-engine.js"}, "dependencies": {"@ant-design-vue/nuxt": "^1.4.5", "@prisma/client": "^6.10.0", "@prisma/nuxt": "^0.2.0", "@sidebase/nuxt-session": "^0.2.8", "@trpc/client": "^10.45.2", "@trpc/server": "^10.45.2", "@types/echarts": "^4.9.22", "ant-design-vue": "^4.2.5", "async-validator": "^4.2.5", "axios": "^1.7.9", "crypto-js": "^4.2.0", "decimal.js": "^10.5.0", "echarts": "^5.6.0", "face-api.js": "^0.22.2", "face-effet": "^1.5.5", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "mitt": "^3.0.1", "nuxt": "^3.15.1", "prisma-trpc-generator": "^1.4.1", "svg-captcha": "^1.4.0", "trpc-nuxt": "^0.10.22", "uuid": "^11.1.0", "vue": "latest", "vue-router": "latest", "zod": "^3.24.1", "zod-prisma-types": "^3.2.1"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.7", "@types/md5": "^2.3.5", "@types/node": "^22.7.6", "@types/uuid": "^10.0.0", "daisyui": "^4.12.23", "ts-node": "^10.9.2", "typescript": "^5.6.3", "vite-plugin-vue-mcp": "^0.2.2"}, "prisma": {"seed": "ts-node-esm prisma/seed.ts"}}